{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"AirMaster.Flow": {"projectType": "application", "schematics": {"@schematics/angular:component": {"standalone": false}, "@schematics/angular:directive": {"standalone": false}, "@schematics/angular:pipe": {"standalone": false}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/air-master.flow", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js", "@angular/localize/init"], "tsConfig": "tsconfig.app.json", "assets": ["src/assets", {"glob": "**/*", "input": "public"}], "styles": ["src/styles.css", "node_modules/@progress/kendo-theme-classic/dist/all.css"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "5MB"}, {"type": "anyComponentStyle", "maximumWarning": "2kB", "maximumError": "4kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "AirMaster.Flow:build:production"}, "development": {"buildTarget": "AirMaster.Flow:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing", "@angular/localize/init"], "tsConfig": "tsconfig.spec.json", "assets": ["src/assets", {"glob": "**/*", "input": "public"}], "styles": ["src/styles.css", "node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/@progress/kendo-theme-classic/dist/all.css", "node_modules/@progress/kendo-theme-material/dist/all.css"], "scripts": []}}}}}, "cli": {"analytics": "50080965-e19d-4861-8202-5e6a596afae3"}}