{"name": "air-master.flow", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^18.2.13", "@angular/cdk": "^17.0.0", "@angular/common": "^18.2.0", "@angular/compiler": "^18.2.0", "@angular/core": "^18.2.0", "@angular/forms": "^18.2.0", "@angular/localize": "^18.2.0", "@angular/platform-browser": "^18.2.0", "@angular/platform-browser-dynamic": "^18.2.0", "@angular/router": "^18.2.0", "@angular/ssr": "^18.2.13", "@progress/kendo-angular-buttons": "17.2.0", "@progress/kendo-angular-common": "17.2.0", "@progress/kendo-angular-dateinputs": "^17.2.0", "@progress/kendo-angular-dialog": "^17.2.0", "@progress/kendo-angular-dropdowns": "^17.2.0", "@progress/kendo-angular-grid": "^17.2.0", "@progress/kendo-angular-icons": "17.2.0", "@progress/kendo-angular-indicators": "^17.2.0", "@progress/kendo-angular-inputs": "^17.2.0", "@progress/kendo-angular-intl": "17.2.0", "@progress/kendo-angular-l10n": "17.2.0", "@progress/kendo-angular-label": "^17.2.0", "@progress/kendo-angular-layout": "^17.2.0", "@progress/kendo-angular-popup": "17.2.0", "@progress/kendo-angular-progressbar": "17.2.0", "@progress/kendo-angular-tooltip": "^17.2.0", "@progress/kendo-data-query": "^1.7.0", "@progress/kendo-drawing": "^1.21.0", "@progress/kendo-licensing": "^1.0.2", "@progress/kendo-svg-icons": "^4.0.0", "@progress/kendo-theme-classic": "^10.1.0", "@progress/kendo-theme-default": "^10.1.0", "@progress/kendo-theme-material": "^10.1.0", "air-master.flow": "file:", "bootstrap": "^5.3.3", "gsap": "^3.12.7", "ngx-toastr": "^19.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.12", "@angular/cli": "^18.2.12", "@angular/compiler-cli": "^18.2.0", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.2.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.5.2"}}