import { Component } from '@angular/core';
import { SharedDataService } from '../Services/shared-data.service';

@Component({
  selector: 'app-root',
  templateUrl: './../Template/app.component.html',
  styleUrl: './../Style/app.component.css'
})
export class AppComponent {
  title = 'AirMaster Flow';
  year: any = new Date().getFullYear();

  constructor(protected shareService: SharedDataService) {}



}
