import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs/internal/Observable";
import { sendNotifyDatamodel, SupplierResponse} from "../../supplier/Model/supplies";

@Injectable({
  providedIn: 'root'
})
export class CoreDataService {
    private baseUrl: string = "https://localhost:44398";

    private GetQuateData = '/api/Inquiry/Quote';
    private GetMaterial = '/api/Inquiry/Material';
    private GetSupplier = '/api/Reply/Supplier';
    private PostSupplierResponses = '/api/Reply/Supplier';
    private urlDecoder = '/api/Verify';
    private notifyManager = '/api/ReplyToInquiryQuoteNotification';
    private replyNotifiYes = '/api/ReplyToRateVerificationAfterExpiryNotificationYes'
    private replyNotifiNO = '/api/ReplyToRateVerificationAfterExpiryNotificationNo'

    constructor(private http: HttpClient) {
        if (window.location.origin.includes("localhost")) {
          // this.baseUrl = "http://supplier.eudemonic.co.in/api";
          this.baseUrl = "https://aetest.optimumair.co.nz/supplier/api";
          // this.baseUrl = "https://localhost:44398/api";

        }
        else {
          if(window.location.origin === "https://aetest.optimumair.co.nz"){
            this.baseUrl = window.location.origin + "/supplier/api";
          }else{
            this.baseUrl = window.location.origin + "/api";
          }
        }
        this.GetQuateData = this.baseUrl + this.GetQuateData;
        this.GetMaterial = this.baseUrl + this.GetMaterial;
        this.GetSupplier = this.baseUrl + this.GetSupplier;
        this.PostSupplierResponses = this.baseUrl + this.PostSupplierResponses;
        this.urlDecoder = this.baseUrl + this.urlDecoder;
        this.notifyManager = this.baseUrl + this.notifyManager;
        this.replyNotifiYes = this.baseUrl + this.replyNotifiYes;
        this.replyNotifiNO = this.baseUrl + this.replyNotifiNO;
    }

    public details(quoteNumber: number): Observable<any> {
      return this.http.get(this.GetQuateData+`/${quoteNumber}`);
    }

    public getMaterial(inquiryQuoteID: number):Observable<any>{
        return this.http.get(this.GetMaterial , {params:{inquiryQuoteID}});
    }
    public getSupplier(inquiryQuoteId: number):Observable<any>{
      return this.http.get(this.GetSupplier ,{params:{inquiryQuoteId}});
    }
    public saveSupplierResponses(repliesSupplierId: SupplierResponse[]): Observable<any>{
      return this.http.post(this.PostSupplierResponses , repliesSupplierId , { responseType: 'text'});
    }
    public URLDecoder(Secret: string , data:string):Observable<any>{
      return this.http.get(this.urlDecoder, {params:{ Secret , data }} );
    }
    public sendNotification(notify: any):Observable<any>{
      return this.http.patch(this.notifyManager , notify);
    }
    public replytoAccountManagerYES(yes: sendNotifyDatamodel):Observable<any>{
      return this.http.patch(this.replyNotifiYes , yes);
    }
    public replytoAccountManagerNOWPriceValid(NOW: sendNotifyDatamodel):Observable<any>{
      return this.http.patch(this.replyNotifiNO , NOW);
    }



}
