import { SharedDataService } from './shared-data.service';
import { HttpErrorResponse, HttpEvent, HttpHandler, HttpInterceptor, HttpRequest, HttpResponse } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { catchError, Observable, tap, throwError } from "rxjs";



@Injectable()
export class HttpIntercepter implements HttpInterceptor {
  constructor(private sharedDataService:SharedDataService){

  }
  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    try {
      // console.log('API Request:', req.url);
      // this.sharedDataService.showSkeleton = true;
      //getting secret key****
      const secretKey = localStorage.getItem("Secret");
      let modifiedReq = req;
      if (secretKey) {
        modifiedReq = req.clone({
          setHeaders: { 'SecureLink': `${secretKey}` } // Set the secret key in the header
        });
      }
      return next.handle(modifiedReq).pipe(
        catchError((error: HttpErrorResponse) => {
          // this.sharedDataService.showSkeleton= false;
          console.error('API Error:', error);
          if (error.status === 401) {
            // this.sharedDataService.showError("Something went Wrong...");
        } else if (error.status === 400 || error.status === 500 || error.status === 0) {
            // this.sharedDataService.showError("Something went Wrong..");
        }
        return throwError(Error);
        }),
        tap((event: HttpEvent<any>) => {
          if (event instanceof HttpResponse) {
              this.sharedDataService.showSkeleton= false;
          }
      }),
      );

    } catch (err) {
      console.error('Interceptor Error:', err);
      this.sharedDataService.showError("Bad Request");
      return throwError(() => new Error('Something went wrong in the Interceptor!'));
    }
  }
  }
