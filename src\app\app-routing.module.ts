import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ErrorComponent } from './shared/Template/error.component';

const routes: Routes = [
{
  path:"request",
  loadChildren: ()=>import('./supplier/supplier.module').then(m=>m.SupplierModule),
},
// {
//   path:'**',
//   component:ErrorComponent
// }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
