.menu-button {
    background: transparent;
    color: white;
    border-color: transparent;
    /* padding: 5px 12px; */
    height: 33px;
    font-weight: 600;
    letter-spacing: 0.2px;
    display: inline-block;
    cursor: pointer;
    line-height: initial;
    padding: 0px 8px;
    border-right: 1px solid rgb(124, 164, 230);
}

.menu-button:hover .menu-bar-icon {
    transform: scale(1.3);
    transition: transform 0.3s ease-in-out;
}

.menu-button:hover {
    /* background-color: rgb(50, 83, 136); */
    color: white;
    height: 33px;
}

button.menu-button[disabled] {
    cursor: no-drop !important;
    color: rgb(179, 193, 216) !important
}
