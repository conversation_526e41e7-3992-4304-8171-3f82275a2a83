import { Component } from '@angular/core';

@Component({
  selector: 'app-error',
  template: `
    <div class="not-found-container">
      <h1>404 - Page Not Found</h1>
      <p>Oops! The page you're looking for doesn't exist.</p>
      <p>Please check the URL or go back to the <a routerLink="/">homepage</a>.</p>
      <img src="assets/images/404-error.png" alt="404 Error" class="error-image" />
    </div>
  `,
  styles: [
    `
      .not-found-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
        text-align: center;
        background-color: #f8f9fa;
      }

      h1 {
        font-size: 3rem;
        color: #dc3545;
      }

      p {
        font-size: 1.2rem;
        color: #6c757d;
      }

      a {
        color: #007bff;
        text-decoration: none;
      }

      a:hover {
        text-decoration: underline;
      }

      .error-image {
        width: 300px;
        margin-top: 20px;
      }
    `,
  ],
})
export class ErrorComponent {}
