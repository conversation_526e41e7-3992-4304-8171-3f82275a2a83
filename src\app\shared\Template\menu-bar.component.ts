import { Component, EventEmitter, Input, Output } from "@angular/core";
@Component({
    selector: "menu-bar",

    template: `
    <nav class="menu" style="background-color: green" class="navbar navbar-inverse">
      <div class="container-fluid">
        <ul class="nav navbar-nav" id="navMenu" style="height:34px; display: flex; list-style:none">
          <li
            *ngFor="let item of menu; let i = index"
            style="border-right: 1px solid rgb(84, 114, 93); height:31px;  margin-right: 0px;"
          >
            <button
              class="menu-button"
              [disabled]="item.disabled"
              *ngIf="!item.hidden"
              (click)="menuItemSelected(item)"
            >
              <span class="menu-bar-icon {{ item.Icon }}"></span
              ><span class="menu-bar-text {{ item.TextClass }}">{{
                item.menuItem
              }}</span>
            </button>
          </li>
        </ul>
      </div>
    </nav>

  `,
    styleUrls: ['../Style/menu-bar.component.css'],
})
export class MenuBarComponent {
    @Input()
    public menu: { menuItem: string; disabled: boolean; Icon: any; TextClass: string; hidden: boolean }[] = [];

    @Output()
    public select: EventEmitter<any> = new EventEmitter<any>();

    constructor() { }

    public menuItemSelected(item: any): void {
        this.select.emit({ item: item.menuItem.trim() });
    }
}
