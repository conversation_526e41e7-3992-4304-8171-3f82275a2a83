import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-skeleton-grid',
  template: `<div class="grid-skeleton">
    <div *ngFor="let _ of rowsArray" class="skeleton-row">
      <div *ngFor="let _ of columnsArray" class="skeleton-cell">
        <kendo-skeleton
          style="background-color: #f3f1f1;"
          shape="rectangle"
          SkeletonAnimation="wave"
          [width]="cellWidth"
          [height]="cellHeight"
        ></kendo-skeleton>
      </div>
    </div>
  </div>`,
  styles: [
    `
      .grid-skeleton {
        margin: -11px;
        margin-left: -27px;
        display: flex;
        flex-direction: column;
        gap: 12px;
        padding: 15px;
        background-color: #ffffff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgb(231, 229, 229);
        overflow: hidden;
      }

      .skeleton-row {
        display: flex;
        gap: 15px;
      }

      .skeleton-cell {
        flex-shrink: 0;
        border-radius: 5px;
        overflow: hidden;
      }
    `,
  ],
})
export class SkeletonGridComponent {
  @Input() rows: number = 8;
  @Input() columns: number = 7;
  @Input() cellWidth: number = 150;
  @Input() cellHeight: number = 30;

  get rowsArray(): number[] {
    return Array(this.rows);
  }

  get columnsArray(): number[] {
    return Array(this.columns);
  }
}
