import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule,ReactiveFormsModule  } from '@angular/forms';
import { LabelModule } from "@progress/kendo-angular-label";
import { InputsModule } from "@progress/kendo-angular-inputs";
import { GridLayoutModule, LayoutModule } from "@progress/kendo-angular-layout";
import { ButtonsModule } from "@progress/kendo-angular-buttons";
import { IconsModule } from "@progress/kendo-angular-icons";
import { DropDownsModule } from "@progress/kendo-angular-dropdowns";
import { GridModule,ExcelModule  } from "@progress/kendo-angular-grid";
import { SkeletonGridComponent } from './Template/skeleton-grid.component';
import { IndicatorsModule } from "@progress/kendo-angular-indicators";
import { DateInputsModule } from "@progress/kendo-angular-dateinputs";
import { DialogsModule } from "@progress/kendo-angular-dialog"
import { TooltipModule } from '@progress/kendo-angular-tooltip';
import { TabStripModule } from "@progress/kendo-angular-layout";
import { MenuBarComponent } from './Template/menu-bar.component';
@NgModule({
  declarations: [SkeletonGridComponent , MenuBarComponent],
  imports: [CommonModule,
    FormsModule,
    ReactiveFormsModule,
    LabelModule,
    InputsModule,
    LayoutModule,
    ButtonsModule,
    IconsModule,
    DropDownsModule,
    GridLayoutModule,
    GridModule,
    ExcelModule,
    IndicatorsModule,
    DateInputsModule,
    DialogsModule,
    TooltipModule,
    TabStripModule,
  ],
  exports: [FormsModule,
    ReactiveFormsModule,
    CommonModule,
    LabelModule,
    InputsModule,
    LayoutModule,
    ButtonsModule,
    IconsModule,
    DropDownsModule,
    GridLayoutModule,
    GridModule,
    ExcelModule,
    IndicatorsModule,
    SkeletonGridComponent,
    MenuBarComponent,
    DateInputsModule,
    DialogsModule,
    TooltipModule,
    TabStripModule,
  ]

})
export class SharedModule {
  static forRoot() {
    return {
      ngModule: SharedModule,
      providers: [],
    };
  }
}
