export class supplies{
  creationDate!: Date;
  supplieID!: number;
  updationDate!:Date;
  uniqueIdentifier!:string;
  inquiryMaterialId!: number;
  inquiryQuoteID!:number;
  quoteRequestId!: number;
  productName!: string;
  productDescription!: string;
  partModel!: string;
  manufacturer!: string;
  size!: string;
  qty!: number;
  desiredLeadTime!: number;
  materialItemNumber!: number;
  repliesSupplierId!: number;
  tier1Price!: number;
  tier2Price!: number;
  supplierComment!: string;
  leadTime!: number
  isAlternate?: boolean;
  inquiryQuoteId!: number;
  attachments:any;
}
export class SupplierResponse {
  repliesSupplierId!:number;
  inquiryMaterialId!: number;
  partModel!: string;
  partSerial!: string;
  tier1Price!: number;
  tier2Price!: number;
  supplierComment!: string;
  comment!: string;
  leadTime!: number;
  isAlternate!:boolean;
  supplierUniqueIdentifier!:string;
}
export class materials{
  creationDate!: Date;
  updationDate!:Date;
  inquiryQuoteId!: number;
  inquiryMaterialId!:number;
  partDescription!: string;
  manufacturer!:string;
  partModel!: string;
  partSerial!: string;
  size!: string;
  qty!:number;
  desiredLeadTime!:number;
  materialItemNumber!:number;
  attachments:any;
}

export class quoteData{
  quoteNumber!: string;
  includeDeliveryCosts!:boolean;
  locationName!:string;
  inquiryQuoteID!: number;
  manufacturer!:string;
  equipmentModel!: string;
  equipmentSerial!: string;
  jobType!:string;
  notes!:string;
  priceReturnByDate!:Date;
  quoteID!:number;
  technicianContact!:string;
  technicianName!:string;
  attachments!: Attachment[];
}

export class Attachment {
  attachmentId!: number;
  fileName!: string;
  fileType!: string;
  fileData!: string;
}

export class sendNotifyDatamodel{
      inquiryQuoteID!: number;
      quoteNumber!:string;
      quoteID!: number;
      requesterName!:string;
      requesterEmail!:string;
      supplierName!:string;
      supplierEmail!:string;
      uniqueIdentifier!:string;
}
