import { sendNotifyDatamodel, supplies } from './../Model/supplies';
import { SharedDataService } from './../../Common/Services/shared-data.service';
import { Component, OnInit, signal, TrackByFunction, ViewChild, ViewChildren } from '@angular/core';
import { DomSanitizer, SafeHtml, SafeResourceUrl } from '@angular/platform-browser';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { State, process } from '@progress/kendo-data-query';
import * as XLSX from 'xlsx';
import {
  GridComponent,
  CellClickEvent,
  GridDataResult,
  RowClassArgs,
} from '@progress/kendo-angular-grid';
import { map, Observable, timeout } from 'rxjs';
import { SupplyEditService } from '../Services/supply-edit.service';
import { ExcelExportData } from '@progress/kendo-angular-excel-export';
import { CoreDataService } from '../../Common/Services/core-data.service';
import { GridSize } from "@progress/kendo-angular-grid";

@Component({
  selector: 'app-supplier',
  templateUrl: '../template/supplier.component.html',
  styleUrl: '../style/supplier.component.css',
})
export class SupplierComponent implements OnInit {

  //------------------------------------------------------
  // PROPERTIES AND STATE MANAGEMENT
  //------------------------------------------------------

  // Tracks which rows have save buttons enabled (for alternate rows)
  saveButtonsState: { [key: number]: boolean } = {};

  // Flag to prevent handling cell clicks during programmatic selection
  private isProgrammaticSelection = false;

  // Flag to prevent cell click handling during save operations
  private saveAlternateBtton = false;

  // Flag to prevent double-click issues
  private isProcessingClick = false;

  // Timestamp of last click to prevent double-click issues
  private lastClickTime = 0;

  // Grid size configuration
  public smallSize: GridSize = "small";

  // Flag to track if an alternate row is currently present
  private isAlternatePresent = false;

  // References to grid components
  @ViewChildren("gridRef") public grids!: Array<GridComponent>;
  @ViewChild('grid') public grid!: GridComponent;

  // Form group for the currently edited row
  public formGroup = signal<FormGroup | undefined>(undefined);

  // Index of the currently edited row
  public editedRowIndex = signal<number | undefined>(undefined);

  // Observable for grid data
  public view?: Observable<GridDataResult>;

  // Grid data result
  public gridData: GridDataResult | undefined = undefined;

  // Controls visibility of the sub-navigation bar
  isSubNavVisible: boolean = true;

  // Tracks which parent rows have active alternate rows
  public parentRowsWithAlternates: Set<number> = new Set();

  //Hide Tier 2 field for Some cases
  // public HideGridfield: boolean = false;

  // Grid state configuration (sorting, paging, etc.)
  public gridState: State = {
    sort: [],
    skip: 0,
    take: 1000, // Show all rows without pagination
  };

  constructor(
    protected coreData: CoreDataService,
    protected formBuilder: FormBuilder,
    public editService: SupplyEditService,
    protected sharedData: SharedDataService,
    private sanitizer: DomSanitizer
  ) {
    // Bind the allData method to maintain 'this' context
    this.allData = this.allData.bind(this);
  }

  /**
   * Initialize the component
   * - Set up data binding
   * - Load initial data
   * - Add document click listener for closing edited rows
   */
  ngOnInit(): void {
    // Create an observable that processes the data with the current grid state
    this.view = this.editService.pipe(
      map((data) => process(data, this.gridState))
    );

    // Load initial data
    this.editService.read();

    // Add document click listener to close edited rows when clicking outside
    document.addEventListener('click', this.onDocumentClick.bind(this));
  }

  /**
   * Clean up when component is destroyed
   */
  ngOnDestroy(): void {
    // Remove click event listener
    document.removeEventListener('click', this.onDocumentClick.bind(this));
  }

  /**
   * Handle document clicks to close edited rows when clicking outside the grid
   */
  public onDocumentClick(event: MouseEvent): void {
    // Check if a row is being edited
    if (this.editedRowIndex !== undefined) {
      const gridElement = this.grid.wrapper.nativeElement;
      const clickedInsideGrid = gridElement.contains(event.target as Node);

      // If clicked outside the grid, close the edited row
      if (!clickedInsideGrid) {
        this.closeEditedRow();
      }
    }
  }

  /**
   * Close the currently edited row
   * - Saves valid rows
   * - Shows validation errors for invalid rows
   */
  public closeEditedRow(): void {
    if (this.editedRowIndex !== undefined) {
      if (this.formGroup()?.invalid) {
        this.formGroup()?.markAllAsTouched();
        // Optionally show a message to the user
        // this.sharedData.showInfo("Please fill details correctly before proceeding!");
      } else {
        this.saveCurrentRow();
        this.resetSaveButtonState();
      }
    }
  }

  /**
   * Handle grid state changes (sorting, filtering, etc.)
   */
  public onStateChange(state: State) {
    this.gridState = state;
    this.editService.read();
  }

  /**
   * Handle cell clicks in the grid
   * - Determines if the click should trigger row editing
   * - Prevents editing in command columns or during other operations
   */
  public cellClickHandler(args: CellClickEvent): void {
    // Check if the click is in the command column (Add alt column)
    const isCommandColumn = args.columnIndex !== undefined &&
                        args.column &&
                        args.column.field === undefined &&
                        args.column.title === "Add"  || args.column.title ==="Attachments";

    // Skip if any of these conditions are true:
    // - Programmatic selection is in progress
    // - Save alternate button was clicked
    // - Cell is already being edited
    // - Click is in a command column
    // - Currently processing another click
    if (this.isProgrammaticSelection || this.saveAlternateBtton || args.isEdited || isCommandColumn || this.isProcessingClick) {
      return;
    }

    // Implement debounce for double-click protection
    const currentTime = new Date().getTime();
    if (currentTime - this.lastClickTime < 200) { // 300ms threshold for double-clicks
      // This is likely a double-click, ignore the second click
      return;
    }
    this.lastClickTime = currentTime;

    // Set processing flag to prevent concurrent processing
    this.isProcessingClick = true;

    // Handle row editing
    this.handleRowEdit(args);

    // Reset processing flag after a short delay
    setTimeout(() => {
      this.isProcessingClick = false;
    }, 350); // Slightly longer than double-click threshold
  }

  /**
   * Send notification that prices are valid (YES button)
   */
  sendNotificationYES() {
    const sentMail = this.editService.reply();
    this.coreData.replytoAccountManagerYES(sentMail).subscribe({
      next: (() => {
        this.sharedData.showSuccess("Notification sent successfully!")
        console.log("success");
      }),
      error: ((err: any) => {
        this.sharedData.showError("Something went wrong!")
        console.log("Something went wrong!", err);
      })
    });
    this.isSubNavVisible = false; // Close notification bar
  }

  /**
   * Handle NO button click for price validation
   */
  sendNotificationNO() {
    this.isSubNavVisible = false; // Close notification bar
  }

  //------------------------------------------------------
  // ROW EDITING METHODS
  //------------------------------------------------------

  /**
   * Core method for handling row editing
   * - Manages closing previously edited rows
   * - Opens the clicked row for editing
   */
  private handleRowEdit(args: CellClickEvent): void {
    const rowIndex = args.rowIndex;
    const dataItem = args.dataItem;
    if (!dataItem || rowIndex === undefined || rowIndex >= this.editService.data.length) return;

    // If there's a row currently being edited and it's different from the clicked row
    if (this.editedRowIndex() !== undefined && this.editedRowIndex() !== rowIndex) {
      // Get the currently edited data item
      const currentDataItem = this.editService.data[this.editedRowIndex()!];

      if (this.formGroup()?.valid) {
        // If it was an alternate row, call saveAlternate instead of saveCurrentRow
        if (currentDataItem && currentDataItem.isAlternate) {
          // Call saveAlternate to properly save the alternate row
          this.saveAlternate(currentDataItem);
        } else {
          // For non-alternate rows, use the regular save method
          this.saveCurrentRow();
        }
      }else {
        // If the form is invalid, mark fields as touched to show validation errors
        this.formGroup()?.markAllAsTouched();

        // If it's an empty alternate row, remove it
        if (currentDataItem && currentDataItem.isAlternate &&
            (!currentDataItem.partModel || !currentDataItem.price)) {
          // Remove the empty alternate row
          this.editService.data.splice(this.editedRowIndex()!, 1);
          this.editService.read();
          this.isAlternatePresent = false;

          // Adjust the clicked row index if it's after the removed row
          if (rowIndex > this.editedRowIndex()!) {
            // Decrement the clicked row index since we removed a row before it
            const adjustedRowIndex = rowIndex - 1;

            // Close the current row without saving
            this.grid.closeRow(this.editedRowIndex()!);
            this.editedRowIndex.set(undefined);
            this.formGroup.set(undefined);
            this.resetSaveButtonState();

            // After a short delay, create form group for the adjusted row index
            setTimeout(() => {
              // Make sure we're not in the middle of another click operation
              if (this.isProcessingClick) {
                // Get the data item at the adjusted index
                const adjustedDataItem = this.editService.data[adjustedRowIndex];
                if (adjustedDataItem && adjustedRowIndex < this.editService.data.length) {
                  this.formGroup.set(this.createFormGroup(adjustedDataItem));
                  this.editedRowIndex.set(adjustedRowIndex);
                  this.grid.editRow(adjustedRowIndex, this.formGroup()!);
                }
              }
            }, 50);

            return; // Exit the method to prevent further processing
          }

          // SHow Validation Not remove Alternate row
          // this.sharedData.showWarning("Please fill details correctly before proceeding!");
          // return;
        }


        // Close the current row without saving
        this.grid.closeRow(this.editedRowIndex()!);
        this.editedRowIndex.set(undefined);
        this.formGroup.set(undefined);
        this.resetSaveButtonState();
      }
    }

    // Special handling for alternate rows
    if (dataItem.isAlternate) {
      this.saveButtonsState[dataItem.repliesSupplierId ?? dataItem.supplieID] = true;
      this.isAlternatePresent = true;

    }

    // Create form group for the clicked row
    this.formGroup.set(this.createFormGroup(dataItem));
    if (!this.formGroup()) {
      console.error("Failed to create FormGroup. DataItem:", dataItem);
      return;
    }

    // Subscribe to form changes to enable save button
    this.formGroup()?.valueChanges.subscribe(() => {
      this.editService.change = true; // Enable Save button on change
    });

    // Set the edited row index and open the row for editing
    // Make sure the rowIndex is still valid after potential row removals
    if (rowIndex < this.editService.data.length) {
      this.editedRowIndex.set(rowIndex);
      setTimeout(() => this.grid.editRow(rowIndex, this.formGroup()!), 5);
    } else {
      // If rowIndex is no longer valid, reset the form state
      this.editedRowIndex.set(undefined);
      this.formGroup.set(undefined);
    }
  }

  /**
   * Reset save button states across the grid
   */
  private resetSaveButtonState() {
    Object.keys(this.saveButtonsState).forEach((key: any) => {
      this.saveButtonsState[key] = false;
      this.isAlternatePresent = false;
      this.parentRowsWithAlternates.clear(); // Allow adding alternate rows again
    });
  }

  //------------------------------------------------------
  // SAVE AND UPDATE METHODS
  //------------------------------------------------------

  /**
   * Save the currently edited row
   * - Updates the data model with form values
   * - Closes the row editor
   */
  public saveCurrentRow(): void {
    if (this.formGroup() && this.formGroup()?.valid && this.editedRowIndex() !== undefined) {
      const updatedData = this.formGroup()?.value;

      const currentDataItem = this.editService.data[this.editedRowIndex()!];
      if (!currentDataItem) return;

      // Update the data item with form values
      this.editService.assignValues(currentDataItem, updatedData);
      this.editService.update(currentDataItem);

      // Close the row and reset editing state
      this.grid.closeRow(this.editedRowIndex()!);
      this.editedRowIndex.set(undefined);
      this.formGroup.set(undefined);
    }
  }

  /**
   * Save all changes and submit to the server
   */
  public saveChanges(): void {
    if (this.formGroup()?.invalid) {
      this.formGroup()?.markAllAsTouched();
      this.sharedData.showInfo("Please fill details correctly before proceeding!");
      return;
    }

    // Save the current row if one is being edited
    this.saveCurrentRow();

    // Disable the save button
    this.editService.change = false;

    // Reset button states
    this.resetSaveButtonState();

    // Call the service to save changes to the server
    setTimeout(() => {
      this.editService.saveChanges();
    }, 10);
  }

  /**
   * Cancel all changes and revert to original data
   */
  public cancelChanges(grid: any): void {
    // Close any open row
    grid.closeRow(this.editedRowIndex());

    // Reset state
    this.editService.change = false;
    this.editedRowIndex.set(undefined);

    // Cancel changes in the service
    this.editService.cancelChanges();

    // Reset button states
    this.resetSaveButtonState();
    this.parentRowsWithAlternates.clear();

    // Show notification
    this.sharedData.showInfo('Changes cancelled');
  }

  /**
   * Save an alternate product row
   */
  saveAlternate(selectedRow: any) {
    if (this.formGroup()?.invalid) {
      this.formGroup()?.markAllAsTouched();
      this.sharedData.showInfo("Please fill details correctly before proceeding!");
      return;
    }

    // Set flag to prevent cell click handling
    this.saveAlternateBtton = true;

    // Disable save button for this row
    this.saveButtonsState[selectedRow.repliesSupplierId ?? selectedRow.supplieID] = false;

    // Save the current row
    this.saveCurrentRow();

    // Find the parent row for this alternate
    const parentRow = this.editService.data.find(item =>
      item.inquiryMaterialId === selectedRow.inquiryMaterialId && !item.isAlternate
    );

    // Remove from the set of parent rows with alternates
    if (parentRow) {
      this.parentRowsWithAlternates.delete(parentRow.supplieID);
    }

    // Reset flags after a short delay
    setTimeout(() => {
      this.editService.change = true;
      this.isAlternatePresent = false;
      this.saveAlternateBtton = false;
    }, 10);
  }

  /**
   * Save a parent row
   */
  saveParentRow(selectedRow: any) {
    if (this.formGroup()?.invalid) {
      this.formGroup()?.markAllAsTouched();
      this.sharedData.showInfo("Please fill details correctly before proceeding!");
      return;
    }

    // Set flag to prevent cell click handler from re-opening the row
    this.saveAlternateBtton = true;

    // Save the current row
    this.saveCurrentRow();

    // Reset the flag after a short delay
    setTimeout(() => {
      this.parentRowsWithAlternates.clear();
      this.saveAlternateBtton = false;
    }, 50);
  }

  //------------------------------------------------------
  // DATA EXPORT AND FORM HANDLING
  //------------------------------------------------------

  /**
   * Prepare data for Excel export
   */
  public allData(): ExcelExportData {
    const result: ExcelExportData = {
      data: process(this.editService.data, {
        filter: this.gridState.filter,
        sort: this.gridState.sort,
      }).data,
    };
    return result;
  }

  /**
   * Check if partModel data comes from database (not user-entered)
   */
  private isPartModelFromDatabase(dataItem: supplies): boolean {
    // Find the original data for this item
    const originalItem = this.editService.originalData.find(
      (orig) => orig.supplieID === dataItem.supplieID ||
                (orig.inquiryMaterialId === dataItem.inquiryMaterialId &&
                 orig.repliesSupplierId === dataItem.repliesSupplierId)
    );

    // If no original data exists, this is new data (user can edit)
    if (!originalItem) {
      return false;
    }

    // If original data has partModel and current partModel matches original, it's from DB
    return !!(originalItem.partModel &&
              originalItem.partModel.trim() &&
              dataItem.partModel === originalItem.partModel);
  }

  /**
   * Create a form group with validation for a data item
   */
  public createFormGroup(dataItem: supplies): FormGroup {
    const form = this.formBuilder.group({
      supplieID: [dataItem.supplieID],
      productName:[dataItem?.productName],
      productDescription: [dataItem?.productDescription],
      manufacturer: [dataItem?.manufacturer],
      // Part model - required, max length 20, disabled for Delivery Cost or when data comes from DB and not alternate
      partModel: [
        {
          value: dataItem?.partModel || '',
          disabled: dataItem.productName === 'Delivery Cost' ||
                   (!dataItem?.isAlternate && this.isPartModelFromDatabase(dataItem))
        },
        [Validators.required, Validators.maxLength(20)]
      ],

      qty: [dataItem.qty],
      size: [dataItem?.size],
      desiredLeadTime: [dataItem?.desiredLeadTime],

      // Tier 1 price - required, min 0, numeric pattern
      tier1Price: [
        dataItem?.price,
        [Validators.required, Validators.min(0), Validators.pattern(/^\d{1,10}$/)]
      ],

      // Tier 2 price - min 0, numeric pattern
      tier2Price: [
        dataItem?.rebatePrice,
        [Validators.min(0), Validators.pattern(/^\d{1,10}$/)]
      ],

      // Lead time - min 0, max 999, numeric pattern, disabled for delivery cost
      leadTime: [
        {value: dataItem?.leadTime, disabled: dataItem.productName === 'Delivery Cost'},
        [Validators.min(0), Validators.max(999), Validators.pattern(/^\d{1,3}$/), Validators.pattern(/^\d+$/)]
      ],

      supplierComment: [dataItem?.supplierComment],
      isAlternateRow: [dataItem?.isAlternate || false],
    });

    // Uncomment to enable validation error notifications
    // this.subscribeToValidationErrors(form);

    return form;
  }

  /**
   * Subscribe to form control changes to show validation errors
   * (Currently unused but available for debugging)
   */
  private subscribeToValidationErrors(form: FormGroup) {
    Object.keys(form.controls).forEach(field => {
      form.get(field)?.valueChanges.subscribe(() => {
        const control = form.get(field);
        if (control && control.invalid) {
          const errors = control.errors;
          if (errors?.['required']) {
            this.sharedData.showInfo(`"${field}" is required!`);
          } else if (errors?.['maxlength']) {
            this.sharedData.showInfo(`"${field}" exceeds maximum length of ${errors['maxlength'].requiredLength}!`);
          } else if (errors?.['min']) {
            this.sharedData.showInfo(`"${field}" must be at least ${errors['min'].min}!`);
          } else if (errors?.['pattern']) {
            this.sharedData.showInfo(`"${field}" format is invalid!`);
          }
        }
      });
    });
  }

  /**
   * Track function for Angular change detection optimization
   */
  trackBySupplieID: TrackByFunction<supplies> = (_index: number, item: supplies): any => {
    return item.supplieID;
  };

  //------------------------------------------------------
  // ALTERNATE PRODUCT MANAGEMENT
  //------------------------------------------------------

  /**
   * Add an alternate product row for a selected parent row
   */
  addAlternateProduct(selectedRow: any) {
    // Prevent adding alternate product if we're already processing a click
    // This helps prevent issues with double-clicks
    if (this.isProcessingClick) {
      return;
    }

    // Set flags to prevent cell click handling during this operation
    this.isProgrammaticSelection = true;
    this.isProcessingClick = true;

    // Validate current form if one is being edited
    if (this.formGroup()?.invalid) {
      this.formGroup()?.markAllAsTouched();
      this.isProgrammaticSelection = false;
      this.isProcessingClick = false;
      return; // Don't proceed until current row is valid
    }

    // Check if this parent row already has an alternate
    if (this.parentRowsWithAlternates.has(selectedRow.supplieID)) {
      return; // Only one alternate per parent row allowed
    }

    // Validate selected row
    if (!selectedRow) {
      this.isProgrammaticSelection = false;
      return;
    }

    // Check if another alternate is already being edited
    if (this.isAlternatePresent) {
      // Save the current alternate row before adding a new one
      const currentEditedRow = this.editService.data[this.editedRowIndex()!];
      if (currentEditedRow && currentEditedRow.isAlternate) {
        if (this.formGroup()?.valid) {
          this.saveAlternate(currentEditedRow);
        } else {
          this.sharedData.showWarning("Please save the first alternate before adding another.");
          setTimeout(() => {
            this.isProgrammaticSelection = false;
          }, 50);
          return;
        }
      } else {
        this.sharedData.showWarning("Please save the first alternate before adding another.");
        setTimeout(() => {
          this.isProgrammaticSelection = false;
        }, 50);
        return;
      }
    }

    // Add the parent row ID to the set of rows with active alternates
    this.parentRowsWithAlternates.add(selectedRow.supplieID);

    // Create a new alternate product based on the parent row
    const newAlternateProduct: any = {
      supplieID: this.editService.data.length + 1,
      materialItemNumber: selectedRow.materialItemNumber,
      productName: selectedRow.productName,
      productDescription: selectedRow.productDescription,
      partModel: "", // Empty for user to fill
      manufacturer: selectedRow.manufacturer,
      qty: selectedRow.qty,
      size: selectedRow.size,
      desiredLeadTime: selectedRow.desiredLeadTime,
      price: null, // Empty for user to fill
      rebatePrice: null, // Empty for user to fill
      leadTime: null, // Empty for user to fill
      supplierComment: "Alternate of " + selectedRow.partModel,
      isAlternate: true, // Mark as alternate
      inquiryMaterialId: selectedRow.inquiryMaterialId,
      inquiryQuoteId: selectedRow.inquiryQuoteId,
    };

    // Find the index of the selected row
    const selectedIndex = this.editService.data.findIndex((item: any) => {
      if (item.repliesSupplierId) {
        return item.repliesSupplierId === selectedRow.repliesSupplierId;
      } else if (item.supplieID) {
        return item.supplieID === selectedRow.supplieID;
      }
      return false;
    });

    if (selectedIndex !== -1) {
      // If any row is being edited, save and close it
      if (this.editedRowIndex() !== -1) {
        this.saveCurrentRow();
        this.grid.closeRow(this.editedRowIndex()!);
        this.editedRowIndex.set(undefined);
        this.formGroup.set(undefined);
      }

      // Set flag to indicate an alternate row is present
      this.isAlternatePresent = true;

      // Insert the new alternate row after the parent row
      const insertIndex = Math.min(selectedIndex + 1, this.editService.data.length);
      this.editService.data.splice(insertIndex, 0, newAlternateProduct);

      // Enable save button for this alternate row
      this.saveButtonsState[newAlternateProduct.repliesSupplierId ?? newAlternateProduct.supplieID] = true;

      // Refresh the grid data
      this.editService.read();

      // Create form group and open the row for editing
      this.formGroup.set(this.createFormGroup(newAlternateProduct));
      this.editedRowIndex.set(insertIndex);
      this.grid.editRow(insertIndex, this.formGroup()!);

      // Reset flags after a short delay
      this.isProgrammaticSelection = true;
      setTimeout(() => {
        this.isProgrammaticSelection = false;
        this.isProcessingClick = false;
      }, 50);
    }
  }

  //------------------------------------------------------
  // UI STYLING
  //------------------------------------------------------

  /**
   * Apply CSS classes to rows based on their state
   */
  public rowCallback = (context: RowClassArgs) => {
    if (context.dataItem.isAlternate) {
      return { "alternate-row": true };
    }
    if (context.dataItem.productName === "Delivery Cost") {
      return { "delivery-cost-row": true };
    }
    if (context.dataItem.revised) {
      return { "revised": true };
    }
    return {};
  };

// Preview dialog properties
showPreviewDialog = false;
previewFile: any = null;
previewUrl: SafeResourceUrl | string = '';
// For excel preview
excelSheets: { name: string; html: SafeHtml }[] = [];
excelHtml: { name: string; html: SafeHtml }[] = [];
showExcelPreview: boolean = false;
excelSheetsBase64: string = '';
previewAttachment(file: any): void {
  try {
   if (file.fileType.includes('excel') || file.fileName.endsWith('.xlsx') || file.fileName.endsWith('.xls')) {
      // Preview Excel using SheetJS
      this.excelSheets = this.parseExcelFromBase64(file.fileData);
      this.showExcelPreview = true;
      return;
    }

    const byteCharacters = atob(file.fileData);
    const byteNumbers = Array.from(byteCharacters, char => char.charCodeAt(0));
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: file.fileType });
    const url = window.URL.createObjectURL(blob);

    if (this.isImage(file.fileType)) {
      this.previewFile = file;
      this.previewUrl = url;
      this.showPreviewDialog = true;
    } else if (file.fileType === 'text/plain') {
      const link = document.createElement('a');
      link.href = url;
      link.download = file.fileName.endsWith('.txt') ? file.fileName : file.fileName + '.txt';
      link.click();
    } else {
      const link = document.createElement('a');
      link.href = url;
      link.download = file.fileName;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.open(url, '_blank');
    }
  } catch (error) {
    console.error('Error previewing file:', error);
    this.sharedData.showError('Failed to open file');
  }
}

isImage(type: string): boolean {
  return type.startsWith('image/');
}
parseExcelFromBase64(base64Data: string): { name: string; html: SafeHtml }[] {
  const binary = atob(base64Data);
  const byteArray = new Uint8Array(binary.length);
  for (let i = 0; i < binary.length; i++) {
    byteArray[i] = binary.charCodeAt(i);
  }

  const workbook = XLSX.read(byteArray, { type: 'array' });
  this.excelSheetsBase64 = base64Data;//for download excel this is important;
   return workbook.SheetNames.map(sheetName => {
    const worksheet = workbook.Sheets[sheetName];
    const rawHtml = XLSX.utils.sheet_to_html(worksheet);
    return {
      name: sheetName,
      html: this.sanitizer.bypassSecurityTrustHtml(rawHtml),
    };
  });
}
downloadExcel(): void {
  const byteCharacters = atob(this.excelSheetsBase64 || '');
  const byteArray = new Uint8Array(byteCharacters.length);
  for (let i = 0; i < byteCharacters.length; i++) {
    byteArray[i] = byteCharacters.charCodeAt(i);
  }

  const blob = new Blob([byteArray], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'excel-file.xlsx';
  a.click();
  URL.revokeObjectURL(url);
}



downloadAttachment(file: any): void {
  const byteCharacters = atob(file.fileData);
  const byteNumbers = Array.from(byteCharacters, char => char.charCodeAt(0));
  const byteArray = new Uint8Array(byteNumbers);
  const blob = new Blob([byteArray], { type: file.fileType });
  const url = window.URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.href = url;
  link.download = file.fileName;
  link.click();
  window.URL.revokeObjectURL(url);
}

closePreviewDialog(): void {
  this.showPreviewDialog = false;
  if (this.previewUrl) {
    // Only revoke if it's a string URL (blob URL)
    if (typeof this.previewUrl === 'string' && this.previewUrl.startsWith('blob:')) {
      window.URL.revokeObjectURL(this.previewUrl);
    }
    this.previewUrl = '';
  }
  this.previewFile = null;
}

isImageFile(fileType: string): boolean {
  return fileType.startsWith('image/');
}

isPdfFile(fileType: string): boolean {
  return fileType === 'application/pdf';
}

getIconForFileType(fileType: string): string {
  if (fileType.startsWith('image')) {
    return 'assets/Images/Gallery.png'; // Replace with your image icon path
  } else if (fileType === 'application/pdf') {
    return 'assets/Images/pdf.png';
  } else if (fileType.includes('excel') || fileType.includes('officedocument.spreadsheetml.sheet')) {
    return 'assets/Images/Excel.png';
  } else if (fileType.includes('word') || fileType.includes('officedocument.wordprocessingml.document')) {
    return 'assets/Images/Word.png';
  }else if( fileType.includes('text/plain')){
    return 'assets/Images/file-icon.png';
  }
  else {
    return 'assets/icons/Text.png'; // generic file icon
  }
}

}
