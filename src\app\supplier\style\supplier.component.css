/* ::ng-deep.k-grid .k-grid-header {
  background-color: #309b30 !important;
  }
  ::ng-deep.k-grid .k-table-thead {
  background-color: #309b30 !important;
  }
  ::ng-deep .k-column-title{
    color: white !important;
  }
  ::ng-deep span.k-input-inner {
    color: white !important;
  }
  ::ng-deep .k-grid-toolbar{
    background-color: #475569;
  } */
.container {
  /* border: 1px solid red; */
  /* margin: 0 1.5%; */
  width: auto;
  padding: 0.4rem 0rem 1.5rem 0rem;
  box-sizing: border-box;
  overflow: auto;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  min-width: 200px;
  min-height: 150px;
}
.Supplier{
  /* border: 1px solid red; */
  padding: 5px;

}
.supplies_details {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-bottom: 0.5rem;
  /* transform: scale(0.95); */
  transform-origin: top;
}
.Box {
  height: auto;
  background: white;
  box-shadow: -2px 1px 6px 1px rgba(189, 189, 189, 0.5);
  border-radius: 0.5rem;
  width: 50%;
  padding: 0.3rem;
}
.section-header {
  background-color: rgb(48, 155, 48);
  border-radius: 5px;
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}
.quoteDetails,
.technicianDetails {
  flex: 1;
  border: 1px solid #ddd;
  padding: 0.5rem;
}
.icon {
  margin-left: 0.3rem;
  width: 1.25rem;
  height: 1.25rem;
  margin-right: 0.5rem;
  /* color: #3b82f6; */
  color: white;
}
.icon:hover {
  transform: scale(1.7);
  transition: all 0.3s ease-in-out;
}
h4 {
  padding: 0.4vw;
  margin: 0;
  font-size: 1rem;
  /* color: #1e293b; */
  color: white;
  font-weight: 600;
}
td {
  padding: 0.1rem 0.2rem;
  font-size: 0.8rem;
  white-space: nowrap;
  line-height: 0.95;

}
.label-column {
  width: 30%;
  font-weight: 500;
  color: #64748b;
}
.Technician-column {
  padding-left: 6%;
  padding-right: 2%;
  color: #1e293b;
  font-weight: 500;
  word-wrap: break-word;
  white-space: normal;
}
.notes {
  line-height: 1.1;
  color: #475569;
  font-style: italic;
  font-size: 0.85rem;
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;    /* modern, works well */
  white-space: normal;
  text-wrap: wrap;
}
.highlight {
  color: #3b82f6;
  font-weight: 600;
}
.responsive-text {
  word-wrap: break-word;
  white-space: normal;
}
.saving {
  margin-top: -1rem;
  letter-spacing: -0.04rem;
  word-spacing: -0.04rem;
}
.alternate {
  letter-spacing: -0.04rem;
  word-spacing: -0.04rem;
  margin-left: 6rem;
  margin-top: -1rem;
}

.button {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}
.tools {
  display: flex;
  justify-content: space-between;
  width: 99.5%;
  /* margin-left: 10px; */
}
.button-container{
  display: flex;
  justify-content: flex-end;
  width: 99.5%;
  gap: 5px;
}
.attachment-header-icon{
  vertical-align: middle;
  margin-left: 4px;
  cursor: pointer;
}
.attachment-header-icon:hover{
  transform: scale(1.1);
  transition: all 0.2s ease-in-out;
}
.attachment-icon{
  cursor: pointer;
  margin-left: 2px;
}
.attachment-icon :hover{
  transform: scale(1.1);
  transition: all 0.2s ease-in-out;
}
.attachment-container{
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

/* Cancel button */
.cancleButton {
  background-color: white;
  padding: 6px;
  color: red;
  cursor: pointer;
  font-size: 0.9rem;
}
/* Save button */
.saveButton {
  font-size: 0.9rem;
  padding: 6px;
  background-color: white;
  color: green;
  cursor: pointer;
}

.saveButton:hover, .cancleButton:hover{
  transform: scale(1.1);
  transition: all 0.2s ease-in-out;
  box-shadow: 2px 2px 2px -1px rgba(0, 0, 0, 0.57);
  font-weight: bold;
}

.alternatebutton:hover {
  transform: scale(1.1);
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 2px 2px 2px -1px rgba(0, 0, 0, 0.57);
}
.altSave{
  /* width: 7.1vw;
  font-size: 0.95rem;
  padding: 3px; */
  padding: 2px 15px;
  background-color: #28a745;
  color: white;
  font-weight: 500;
}

.altSave:hover {
  transform: scale(1.1);
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 2px 2px 2px -1px rgba(0, 0, 0, 0.57);
}


/* Kendo grid */
::ng-deep .k-grid .k-table-row.k-selected > .k-table-td {
  background-color: khaki !important;
  font-weight: bold !important;
}
:host ::ng-deep.k-spin-button {
  display: none !important;
}
:host ::ng-deep .k-grid-content {
  overflow-y: visible !important;
  height: auto !important;
  max-height: none !important;
}
:host ::ng-deep.k-height-container{
  height: auto !important;
}
:host ::ng-deep .k-grid-header{
  padding: 0px 0px 0px 0px!important;
}
::ng-deep tr.k-grid-alt {
  background-color: #f0f8ff !important;
  font-style: italic;
  font-weight: bold;
}
@media (max-width: 500px) {
  .alternatebutton, .altSave {
    width: 100%;
    font-size: 14px;
    padding: 8px;
  }
}
/* .error{
  border: 1px solid red;
} */
@media (max-width: 200px) {
  .Box {
    width: 90%;
  }
  .altSave{
    width: 8vw;
  }
  .alternatebutton {
    width: 10vw;
    margin-left: 40vw;
  }

  .k-grid {
    height: 15rem !important;
  }
}



::ng-deep .alternate-row {
  background-color: rgb(236, 255, 183) !important;
  color: brown;
  font-size: 0.8rem;
}
::ng-deep .delivery-cost-row {
  background-color: #ffefc5 !important;
  font-weight: bold !important;
  color: #d35400 !important;
  border-top: 2px solid #f39c12 !important;
}
::ng-deep .revised{
  background-color: white !important;
  font-weight: 500 !important;
  color: #df001e !important;
}

.validbtn{
  border-radius: 5px;
  padding: 2px 20px 3px 20px;
  background-color: white;
  margin-right: 10px;
  border: 1px solid burlywood;
}
.validbtn:hover{
  transform: scale(1.1);
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  /* background-color: rgb(0, 132, 255); */
  font: 1rem;
}
::ng-deep.k-grid tr.k-master-row {
  /* height: 30px !important;  */
  max-height: 50px !important; /* Ensure it doesn't grow */
}
.k-textbox{
  width: 100%;
  padding: 3px;
  resize: none;
  border: 1px solid silver;
  font-size: 0.8rem;
  height: 80px;
}
.truncate-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  text-wrap: wrap;
  max-width: 100%;
}

/* Excel Preview Styles */
::ng-deep .excel-preview table {
  width: 100%;
  border-collapse: collapse;
  font-family: Arial, sans-serif;
  font-size: 12px;
}

::ng-deep .excel-preview table td,
::ng-deep .excel-preview table th {
  border: 1px solid #d0d0d0;
  padding: 4px 8px;
  text-align: left;
  vertical-align: top;
  min-width: 50px;
}

::ng-deep .excel-preview table th {
  background-color: #f0f0f0;
  font-weight: bold;
}

::ng-deep .excel-preview table tr:nth-child(even) {
  background-color: #f9f9f9;
}

::ng-deep .excel-preview table tr:hover {
  background-color: #e6f3ff;
}

::ng-deep .excel-preview .empty-cell {
  background-color: #ffffff;
}

/* TabStrip styling for Excel preview */
::ng-deep .k-tabstrip .k-tabstrip-content {
  padding: 0;
  border: none;
}

::ng-deep .k-tabstrip .k-tabstrip-items .k-item {
  border-bottom: 2px solid transparent;
}

::ng-deep .k-tabstrip .k-tabstrip-items .k-item.k-active {
  border-bottom-color: #0078d4;
  background-color: #f8f9fa;
}
