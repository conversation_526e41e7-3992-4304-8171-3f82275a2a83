import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { SupplierRoutingModule } from './supplier-routing.module';
import { SupplierComponent } from './component/supplier.component';
import { SharedModule } from '../shared/shared.module';
import { SupplyEditService } from './Services/supply-edit.service';
import { CommonModule } from '@angular/common';



@NgModule({
  declarations: [
    SupplierComponent
  ],
  imports: [
    CommonModule,
    SupplierRoutingModule,
    SharedModule.forRoot()
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [SupplyEditService]
})
export class SupplierModule {

 }
