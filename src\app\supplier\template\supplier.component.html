<nav class="menu" style="background-color: #feffc0; display: block;" class="navbar navbar-inverse" *ngIf="isSubNavVisible && editService.validate === 'ValidatePrice'">
  <div class="container-fluid">
    <ul class="nav navbar-nav" id="navMenu" style="height:auto; display: flex; list-style:none; align-items: center;justify-content: space-between;">
      <div class="label">
        <li style="font-weight: bold; margin-left: 10px; display: flex; align-items: center; font-size: 0.9rem;">
          <svg x="0px" y="0px" width="25" height="25" viewBox="0 0 48 48" style="margin-right: 5px;">
              <path fill="#2196f3" d="M44,24c0,11.045-8.955,20-20,20S4,35.045,4,24S12.955,4,24,4S44,12.955,44,24z"></path>
              <path fill="#fff" d="M22 22h4v11h-4V22zM26.5 16.5c0 1.379-1.121 2.5-2.5 2.5s-2.5-1.121-2.5-2.5S22.621 14 24 14 26.5 15.121 26.5 16.5z"></path>
          </svg>
          Please confirm if these prices are still valid. If they are, kindly click on the 'Yes' button. If there are any changes, select 'No' and update the pricing accordingly.
      </li>
      </div>
     <div class="validatebtn">
      <ul style="height:34px; display: flex; list-style:none; align-items: center;">
        <li>
          <button   class=" validbtn" (click)="sendNotificationYES()">Yes</button>
        </li>
        <li>
          <button   class=" validbtn" (click)="sendNotificationNO()">No </button>
        </li>
      </ul>
     </div>
    </ul>
  </div>
</nav>

<div class="container">
  <div class="Supplier">
    <div class="supplies_details">
      <!-- quoteDetails -->
      <div class="quoteDetails Box">
        <div class="section-header">
          <svg class="icon" viewBox="0 0 24 24">
            <path fill="currentColor" d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20M15,13V15H7V13H15M15,17V19H7V17H15M13,11V12H7V11H13Z"/>
          </svg>
          <h4>Request Details</h4>
        </div>
        <table>
          <tbody>
            <tr>
              <td class="label-column"><strong>Quote Number:</strong></td>
              <td>{{ this.editService.detailsQuoteData.quoteNumber }}</td>
            </tr>
            <tr>
              <td class="label-column"><strong>Job Type:</strong></td>
              <td class="responsive-text">{{ this.editService.detailsQuoteData.jobType }}</td>
            </tr>
            <tr *ngIf="this.editService.detailsQuoteData?.suppliers?.length > 0 " >
              <td class="label-column"><strong>Updated Last price</strong></td>
              <td class="highlight"><strong>{{ this.editService.detailsQuoteData?.suppliers[0]?.supplierLastResponded | date  }}</strong></td>
            </tr>
            <tr>
              <td class="label-column"><strong>Price return by date:</strong></td>
              <td class="highlight">
                <span *ngIf="this.editService.detailsQuoteData.priceReturnByDate != '0001-01-01T00:00:00'; else noDate">
                  {{ this.editService.detailsQuoteData.priceReturnByDate | date }}
                </span>
                <ng-template #noDate></ng-template>
              </td>
            </tr>
            <tr class="delivery-row">
              <td class="label-column"><strong>Include Delivery Costs:</strong></td>
              <td >{{ this.editService.detailsQuoteData.includeDeliveryCosts==true?"Yes" : "No" }}</td>
            </tr>
             <tr class="">
              <td class="label-column"><strong>Delivery Location</strong></td>
              <td >{{ this.editService.detailsQuoteData.locationName}}</td>
            </tr>
             <tr class="">
              <td class="label-column"><strong>Manufacturer</strong></td>
              <td>{{ this.editService.detailsQuoteData.manufacturer}}</td>
            </tr>
             <tr class="">
              <td class="label-column"><strong>Equipment Serial</strong></td>
              <td>{{ this.editService.detailsQuoteData.equipmentSerial}}</td>
            </tr>
             <tr class="">
              <td class="label-column"><strong>Equipment Model</strong></td>
              <td>{{ this.editService.detailsQuoteData.equipmentModel}}</td>
            </tr>
            <tr>
              <td class="label-column"><strong>Notes:</strong></td>
              <td class="notes">{{ this.editService.detailsQuoteData.notes }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- technicianDetails -->
      <div class="technicianDetails Box">
        <div class="section-header">
          <svg class="icon" viewBox="0 0 24 24">
            <path fill="currentColor" d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/>
          </svg>
          <h4>Contacts Details</h4>
        </div>
        <table>
          <tbody>
            <tr>
              <td class="label-column"><strong>Account Manager Name:</strong></td>
              <td class="Technician-column">{{ this.editService.detailsQuoteData.requesterName }}</td>
            </tr>
            <tr>
              <td class="label-column"><strong>Account Manager Email:</strong></td>
              <td class="Technician-column responsive-text">{{ this.editService.detailsQuoteData.requesterEmail }}</td>
            </tr>
            <tr>
              <td class="label-column"><strong>Technician Name:</strong></td>
              <td class="Technician-column">{{ this.editService.detailsQuoteData.technicianName }}</td>
            </tr>
            <tr>
              <td class="label-column"><strong>Technician Contact:</strong></td>
              <td class="Technician-column">{{ this.editService.detailsQuoteData.technicianContact }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <kendo-grid
      #grid
      [data]="view |async" *ngIf="!(sharedData.showSkeleton && !(view | async)?.data?.length)"
      [trackBy]="trackBySupplieID"
      [gridResizable]="true"
      [pageable]="false"
      [style.height]="'auto'"
      [style.min-height.px]="280"
      [scrollable]="'none'"
      [rowClass]="rowCallback"
      [resizable]="true"
      [editable]="true"
      (dataStateChange)="onStateChange($event)"
      (cellClick)="cellClickHandler($event)"
      >
      <ng-template kendoGridToolbarTemplate let-dataItem>
        <div class="tools" >
          <div style="display: flex; gap: 5px;">
            <div *ngFor="let file of editService.detailsQuoteData?.attachments">
            <img (click)="previewAttachment(file)"
            [src]="getIconForFileType(file.fileType)"
            alt="icon"
            width="25"
            height="25"
            class="attachment-header-icon"
            [title]="'Click to preview: ' + file.fileName"/>
            </div>
          </div>
          <div class="button-container">
            <button kendoButton themecolor="light" class=' saveButton' [disabled]="!editService.hasChanges()"   (click)="saveChanges()">Submit</button>
            <button kendoButton themeclor="tertiary" class='k-button cancleButton' [disabled]="!editService.hasChanges()" (click)="cancelChanges(grid)">Cancel</button>
            <button kendoGridExcelCommand  class=' k-button exportExcel'>Export</button>
          </div>
        </div>
      </ng-template>

      <!-- <kendo-grid-column [style]="{'text-align':'center'}" media="(min-width: 250px)" [width]="48" field="repliesSupplierId" title="repliesSupplierId"  [editable]="false" ></kendo-grid-column> -->
      <kendo-grid-column [style]="{'text-align':'center'}" [minResizableWidth]="30"  [width]="40" media="(min-width: 250px)" field="materialItemNumber" title="Item No." [editable]="false">
        <ng-template kendoGridHeaderTemplate>
          <span  kendoTooltip position="top" title="Item Number"><strong>Item No.</strong></span>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column  media="(min-width: 250px)" [minResizableWidth]="40" [width]="80" field="productName" title="Product Name"  [editable]="false" >
        <ng-template kendoGridHeaderTemplate>
          <span  kendoTooltip position="top" title="Product Name"><strong>Product Name</strong></span>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem="dataItem" let-column="column">
          <div class="truncate-text" title="{{ dataItem[column.field] }}">
            {{ dataItem[column.field] }}
          </div>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column  media="(min-width: 250px)" [minResizableWidth]="40" [width]="80" field="productDescription" title="Product Des..."  [editable]="false" >
        <ng-template kendoGridHeaderTemplate>
          <span  kendoTooltip position="top" title="Product Description"><strong>Product description</strong></span>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem="dataItem" let-column="column">
          <div class="truncate-text" title="{{ dataItem[column.field] }}">
            {{ dataItem[column.field] }}
          </div>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column  media="(min-width: 250px)" [minResizableWidth]="40" [width]="70" field="partModel" title="Part Model" maxlength="10">
        <ng-template kendoGridHeaderTemplate >
          <span  kendoTooltip position="top" title="Part Model"><strong>Part Model</strong></span>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem="dataItem" let-column="column">
          {{ dataItem[column.field] }}
        </ng-template>
        <ng-template kendoGridEditTemplate let-formGroup="formGroup" let-column="column">
          <input kendoTextBox
                 type="text"
                 [formControl]="formGroup.get(column.field)"
                 maxlength="20"
                 style="width: 100%; height: 32px;" />

          <!-- Validation Messages -->
          <div *ngIf="formGroup.get(column.field)?.invalid && (formGroup.get(column.field)?.dirty || formGroup.get(column.field)?.touched)"
               class="text-danger">

            <small *ngIf="formGroup.get(column.field)?.errors?.['required']">
              Part Model is required.
            </small>
          </div>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column  media="(min-width: 250px)" [minResizableWidth]="40" [width]="70" field="manufacturer" title="Manufacturer" [editable]="false">
        <ng-template kendoGridHeaderTemplate >
          <span  kendoTooltip position="top" title="Manufacturer"><strong>Manufacturer</strong></span>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem="dataItem" let-column="column">
          <div class="truncate-text" title="{{ dataItem[column.field] }}">
            {{ dataItem[column.field] }}
          </div>
        </ng-template>
      </kendo-grid-column>




      <kendo-grid-column  media="(min-width: 250px)"   [minResizableWidth]="30"  [width]="50"field="size" title="Size"   [editable]="false">
        <ng-template kendoGridHeaderTemplate>
          <span  kendoTooltip position="top" title="Size"><strong>Size</strong></span>
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column [style]="{'text-align':'right'}"[minResizableWidth]="50" [width]="50"   media="(min-width: 250px)" field="qty" title="Quantity" [editable]="false">
        <ng-template kendoGridHeaderTemplate>
          <span  kendoTooltip position="top" title="Quantity"><strong>Qty..</strong></span>
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column [style]="{'text-align':'right'}" [minResizableWidth]="30"  [width]="58"  media="(min-width: 250px)" field="desiredLeadTime" title="Desired Lead Time"[editable]="false" >
        <ng-template kendoGridHeaderTemplate>
          <span  kendoTooltip position="top" title="Desired Lead Time"><strong>Desired...</strong></span>
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column [style]="{'text-align':'right'}" [minResizableWidth]="60"  [width]="80" media="(min-width: 250px)" field="price" title="Price" editor="numeric" [min]="0">
      <ng-template kendoGridHeaderTemplate>
        <span kendoTooltip position="top" title="Price"><strong>Price</strong></span>
      </ng-template>

      <!-- Normal View Mode -->
      <ng-template kendoGridCellTemplate let-dataItem="dataItem" let-column="column">
        {{ dataItem[column.field] }}
      </ng-template>

      <!-- Edit Mode -->
      <ng-template kendoGridEditTemplate let-formGroup="formGroup" let-column="column">
        <kendo-numerictextbox
          [formControl]="formGroup.get(column.field)"
          [min]="0"
          [format]="'n2'"
          [step]="1"
          style="width: 100%;"
        ></kendo-numerictextbox>

    <!-- Validation Errors -->
    <div *ngIf="formGroup.get(column.field)?.invalid && (formGroup.get(column.field)?.dirty || formGroup.get(column.field)?.touched)"
         class="text-danger">

      <small *ngIf="formGroup.get(column.field)?.errors?.['required']">
        Price is required.
      </small>

      <small *ngIf="formGroup.get(column.field)?.errors?.['min']">
        Price must be at least 0.
      </small>

      <small *ngIf="formGroup.get(column.field)?.errors?.['pattern']">
        Invalid number format.
      </small>

    </div>
  </ng-template>

      </kendo-grid-column>

      <kendo-grid-column *ngIf="editService.HideGridfield"  [style]="{'text-align':'right'}" [minResizableWidth]="60" [width]="80" media="(min-width: 250px)" field="rebatePrice"  title="rebate Price"  editor="numeric">
        <ng-template kendoGridHeaderTemplate>
          <span  kendoTooltip position="top" title="rebate Price"><strong>rebate Price</strong></span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem="dataItem" let-column="column">
          {{ dataItem[column.field] }}
        </ng-template>
        <ng-template kendoGridEditTemplate let-formGroup="formGroup" let-column="column">
          <kendo-numerictextbox
            [formControl]="formGroup.get(column.field)"
            [min]="0"
            [format]="'n2'"
            [step]="1"
            style="width: 100%;"
          ></kendo-numerictextbox>

      <!-- Validation Errors -->
      <div *ngIf="formGroup.get(column.field)?.invalid && (formGroup.get(column.field)?.dirty || formGroup.get(column.field)?.touched)"
           class="text-danger">

        <small *ngIf="formGroup.get(column.field)?.errors?.['required']">
          rebate Priceis required.
        </small>

        <small *ngIf="formGroup.get(column.field)?.errors?.['min']">
          Price must be at least 0.
        </small>

        <small *ngIf="formGroup.get(column.field)?.errors?.['pattern']">
          Invalid number format.
        </small>

      </div>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [style]="{'text-align':'right'}" [minResizableWidth]="50" [width]="70" media="(min-width: 250px)" field="leadTime" title="Lead Time"  editor="numeric">
        <ng-template kendoGridHeaderTemplate>
          <span  kendoTooltip position="top" title="Lead Time"><strong>Lead Time</strong></span>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem="dataItem" let-column="column">
          {{ dataItem[column.field] }}
        </ng-template>
        <ng-template kendoGridEditTemplate let-formGroup="formGroup" let-column="column">
          <kendo-numerictextbox
            [formControl]="formGroup.get(column.field)"
            [min]="0"
            [max]="999"
            [format]="'n2'"
            [step]="1"
            style="width: 100%;"
          ></kendo-numerictextbox>

      <!-- Validation Errors -->
      <div *ngIf="formGroup.get(column.field)?.invalid && (formGroup.get(column.field)?.dirty || formGroup.get(column.field)?.touched)"
           class="text-danger">

        <small *ngIf="formGroup.get(column.field)?.errors?.['min']">
          Lead Time atleast 0.
        </small>

        <small *ngIf="formGroup.get(column.field)?.errors?.['pattern']">
          Invalid number format.
        </small>

      </div>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [style]="{'text-align':'center'}" [minResizableWidth]="30" [width]="80" media="(min-width: 250px)" field="attachments" title="Attachments"  [editable]="false" title="Attachments">
        <ng-template kendoGridHeaderTemplate>
          <span  kendoTooltip position="top" title="Attachments"><strong>Attachments</strong></span>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem>
          <ng-container *ngIf="dataItem.attachments?.length > 0; else noAttachments">
            <div class="attachment-container">
              <div *ngFor="let file of dataItem.attachments" class="attachment-icon">
                <img
                  [src]="getIconForFileType(file.fileType)"
                  alt="icon"
                  width="23"
                  height="23"
                  class="attachment-icon"
                  (click)="previewAttachment(file)"
                  [title]="'Preview: ' + file.fileName"/>
              </div>
            </div>
          </ng-container>
          <ng-template #noAttachments>
            <span>No Files</span>
          </ng-template>
        </ng-template>
      </kendo-grid-column>


      <kendo-grid-column  media="(min-width: 250px)" [minResizableWidth]="80" field="supplierComment" title="Comments" editor="text" [width]="160">
        <ng-template kendoGridHeaderTemplate>
          <span  kendoTooltip position="top" title="Comments"><strong>Comments</strong></span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem="dataItem" let-column="column">
          <div class="truncate-text" title="{{ dataItem[column.field] }}">
            {{ dataItem[column.field] }}
          </div>
        </ng-template>

        <ng-template kendoGridEditTemplate let-formGroup="formGroup" let-column="column">
          <textarea kendoGridFocusable
                    class="k-textbox"
                    [formControl]="formGroup.get(column.field)"
                    rows="4">
          </textarea>
      </ng-template>
      </kendo-grid-column>

      <kendo-grid-excel fileName="Request_Details.xlsx" [fetchData]="allData"></kendo-grid-excel>

      <kendo-grid-command-column media="(min-width: 250px)"  title="Add" [editable]="false" [width]="70" [minResizableWidth]="60" >
        <ng-template kendoGridHeaderTemplate>
          <span  kendoTooltip position="top" title="Add alt"><strong>Add Alt</strong></span>
        </ng-template>
        <ng-template  kendoGridCellTemplate let-dataItem let-isNew="isNew">
          <div style="display: flex; flex-direction: column; align-items: center; gap: 5px;">
            <!-- Add alt button for parent rows (not being edited) -->
            <button kendoButton themeColor="success" class="k-button alternatebutton"
                  *ngIf="!dataItem.isAlternate && dataItem.productName !== 'Delivery Cost' &&
                        (editedRowIndex() === undefined || dataItem.supplieID !== editService.data[editedRowIndex()!]?.supplieID)"
                  [disabled]="parentRowsWithAlternates.has(dataItem.supplieID)"
                  (click)="addAlternateProduct(dataItem)">
              <span class="addAlternateText">Add alt</span>
            </button>

            <!-- Save button for parent rows in edit mode -->
            <button kendoButton themeColor="success" class="k-button altSave"
                  *ngIf="!dataItem.isAlternate  &&
                        editedRowIndex() !== undefined && dataItem.supplieID === editService.data[editedRowIndex()!]?.supplieID"
                  (click)="saveParentRow(dataItem)">
              Save
            </button>

            <!-- Save button for alternate rows -->
            <button *ngIf="dataItem.isAlternate" kendoButton themeColor="success" class="k-button altSave"
                  [disabled]="!saveButtonsState[dataItem.repliesSupplierId??dataItem.supplieID]"
                  (click)="saveAlternate(dataItem)">
              Save
            </button>
          </div>
          </ng-template>
      </kendo-grid-command-column>
      <!-- Sekelton********************************************************************* -->
      <ng-template kendoGridNoRecordsTemplate *ngIf="!sharedData.showSkeleton && !(view | async)?.data?.length">
        <app-skeleton-grid [rows]="5" [columns]="9" [cellWidth]="150" [cellHeight]="30"></app-skeleton-grid>
      </ng-template >
      <!-- Sekelton*********************************************************************-->

    </kendo-grid>
  </div>
</div>

<!-- Document Preview Dialog -->
<kendo-dialog
  *ngIf="showPreviewDialog && previewFile"
  [title]="'Preview: ' + previewFile.fileName"
  [width]="isImageFile(previewFile.fileType) ? 800 : (isPdfFile(previewFile.fileType) ? 900 : 600)"
  [height]="isImageFile(previewFile.fileType) ? 600 : (isPdfFile(previewFile.fileType) ? 700 : 400)"
  [minWidth]="400"
  [minHeight]="300"
  (close)="closePreviewDialog()">

  <div style="width: 100%; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center;">

    <!-- Image Preview -->
    <div *ngIf="isImageFile(previewFile.fileType)" style="width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; overflow: auto;">
      <img [src]="previewUrl" [alt]="previewFile.fileName"
           style="max-width: 100%; max-height: 100%; object-fit: contain; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);" />
    </div>

    <!-- PDF Preview -->
    <div *ngIf="isPdfFile(previewFile.fileType)" style="width: 100%; height: 100%;">
      <iframe [src]="previewUrl" style="width: 100%; height: 100%; min-height: 500px; border-radius: 4px;" frameborder="0"></iframe>
    </div>

    <!-- Document Preview (Word, etc.) -->
    <div *ngIf="!isImageFile(previewFile.fileType) && !isPdfFile(previewFile.fileType)"
         style="width: 100%; height: 100%; display: flex; justify-content: center; align-items: center;">
      <div style="display: flex; flex-direction: column; align-items: center; gap: 16px; text-align: center; padding: 32px; background-color: #f8f9fa; border-radius: 8px; border: 2px dashed #dee2e6;">
        <div style="font-size: 64px;">📄</div>
        <div>
          <h3 style="margin: 0 0 8px 0; color: #333;">{{ previewFile.fileName }}</h3>
          <p style="margin: 4px 0; color: #666;">File Type: {{ previewFile.fileType }}</p>
          <p style="margin: 4px 0; color: #666;">This document type cannot be previewed in the browser.</p>
          <p style="margin: 4px 0; color: #666;">Click download to view the document in your default application.</p>
        </div>
      </div>
    </div>
  </div>

  <kendo-dialog-actions>
    <button kendoButton [primary]="true" (click)="downloadAttachment(previewFile)">
      <span class="k-icon k-i-download"></span>
      Download
    </button>
    <button kendoButton (click)="closePreviewDialog()">Close</button>
  </kendo-dialog-actions>
</kendo-dialog>

<!-- Excel Preview Dialog -->
<kendo-dialog
  *ngIf="showExcelPreview"
  (close)="closeExcelPreview()"
  title="Excel Preview"
  [width]="1000"
  [height]="700"
  [minWidth]="600"
  [minHeight]="400">

  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
    <h4 style="margin: 0; color: #333;">{{ previewFile?.fileName || 'Excel File' }}</h4>
    <button kendoButton (click)="downloadExcel()" themeColor="primary" size="small">
      <span class="k-icon k-i-download"></span>
      Download Excel
    </button>
  </div>

  <!-- Loading indicator -->
  <div *ngIf="isLoadingExcel" style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 300px;">
    <kendo-loader [size]="'large'"></kendo-loader>
    <p style="margin-top: 16px; color: #666;">Loading Excel file...</p>
  </div>

  <!-- Excel content -->
  <kendo-tabstrip *ngIf="!isLoadingExcel && excelSheets.length > 0" style="height: calc(100% - 60px);">
    <kendo-tabstrip-tab *ngFor="let sheet of excelSheets" [title]="sheet.name">
      <div [innerHTML]="sheet.html"
           class="excel-preview"
           style="max-height: 500px; overflow: auto; padding: 10px; background: white; border: 1px solid #ddd; border-radius: 4px;">
      </div>
    </kendo-tabstrip-tab>
  </kendo-tabstrip>

  <!-- Error message if no sheets -->
  <div *ngIf="!isLoadingExcel && excelSheets.length === 0" style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 300px;">
    <div style="font-size: 48px;">⚠️</div>
    <p style="margin-top: 16px; color: #666;">Failed to load Excel file or file is empty.</p>
  </div>

  <kendo-dialog-actions>
    <button kendoButton [primary]="true" (click)="downloadExcel()">
      <span class="k-icon k-i-download"></span>
      Download
    </button>
    <button kendoButton (click)="closeExcelPreview()">Close</button>
  </kendo-dialog-actions>
</kendo-dialog>


