@import "ngx-toastr/toastr";
* {
  padding: 0%;
  margin: 0%;
  font-family: <PERSON><PERSON><PERSON>;
}
body {
  font-size: 13px;
  color: #333;
  font-family: Calibri !important;
}
.k-column-title {
  font-weight: bold;
}
/* ******TAB--TRIP********* */
.k-tabstrip-top > .k-tabstrip-items .k-item {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

/* ****************Kendo-Grid************************ */
kendo-grid {
  box-shadow: -2px 1px 6px 1px rgb(189, 189, 189);
  border-radius: 5px;
}
/* Export to Excel************************************* */
.exportExcel {
  background-color: white;
  background: white!important;
  padding: 6px;
  color: #3e73c2 !important;
  font-size: 0.9rem;
  border: 1px solid;
  cursor: pointer;
}
.exportExcel:hover {
  background: #3e73c2 !important;
  box-shadow: 2px 2px 2px -1px rgba(0, 0, 0, 0.57);
  transform: scale(1.1);
  color: #fff !important;
  transition: all 0.2s ease-in-out;
}

